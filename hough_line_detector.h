#include "hough_line_detector.h"
#include <algorithm>
#include <cmath>
#include <iostream>

HoughLineDetector::HoughLineDetector(int threshold, double rho, double theta, EdgeDetectionMethod method)
    : threshold_(threshold), rho_(rho), theta_(theta), edge_method_(method) {
    rectangle_.isValid = false;
}

bool HoughLineDetector::detectLines(const cv::Mat& image) {
    if (image.empty()) return false;

    image_size_ = image.size();

    // 预处理图像，提取白色区域
    cv::Mat binary = preprocessImage(image);

    // 边缘检测（不使用Canny）
    cv::Mat edges = detectEdges(binary);

    // Hough直线检测
    std::vector<cv::Vec2f> lines;
    cv::HoughLines(edges, lines, rho_, theta_, threshold_);

    if (lines.size() < 4) {
        std::cout << "检测到的直线数量不足: " << lines.size() << std::endl;
        return false;
    }

    // 分类直线
    classifyLines(lines);

    // 选择最佳的边界线
    if (!selectBoundaryLines()) {
        std::cout << "无法选择有效的边界线" << std::endl;
        return false;
    }

    // 验证矩形
    if (!validateRectangle()) {
        std::cout << "检测到的矩形无效" << std::endl;
        return false;
    }

    rectangle_.isValid = true;
    return true;
}

cv::Mat HoughLineDetector::preprocessImage(const cv::Mat& image) {
    cv::Mat gray, binary;

    // 转换为灰度图
    if (image.channels() == 3) {
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    }
    else {
        gray = image.clone();
    }

    // 中值滤波去噪
    cv::medianBlur(gray, gray, 5);

    // 二值化，提取白色区域
    cv::threshold(gray, binary, 0, 255, cv::THRESH_BINARY | cv::THRESH_OTSU);

    // 形态学操作，连接断开的线段
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
    cv::morphologyEx(binary, binary, cv::MORPH_CLOSE, kernel);

    return binary;
}

cv::Mat HoughLineDetector::detectEdges(const cv::Mat& binary) {
    cv::Mat edges;

    switch (edge_method_) {
    case SOBEL_GRADIENT: {
        cv::Mat grad_x, grad_y;
        cv::Mat abs_grad_x, abs_grad_y;

        // Sobel梯度检测
        cv::Sobel(binary, grad_x, CV_16S, 1, 0, 3);
        cv::Sobel(binary, grad_y, CV_16S, 0, 1, 3);

        cv::convertScaleAbs(grad_x, abs_grad_x);
        cv::convertScaleAbs(grad_y, abs_grad_y);

        cv::addWeighted(abs_grad_x, 0.5, abs_grad_y, 0.5, 0, edges);

        // 阈值化
        cv::threshold(edges, edges, 50, 255, cv::THRESH_BINARY);
        break;
    }

    case LAPLACIAN: {
        // 拉普拉斯算子
        cv::Laplacian(binary, edges, CV_16S, 3);
        cv::convertScaleAbs(edges, edges);

        // 阈值化
        cv::threshold(edges, edges, 30, 255, cv::THRESH_BINARY);
        break;
    }

    case MORPHOLOGY_GRADIENT: {
        // 形态学梯度
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
        cv::Mat dilated, eroded;
        cv::dilate(binary, dilated, kernel);
        cv::erode(binary, eroded, kernel);
        cv::subtract(dilated, eroded, edges);
        break;
    }
    }

    return edges;
}

void HoughLineDetector::classifyLines(const std::vector<cv::Vec2f>& lines) {
    horizontal_lines_.clear();
    vertical_lines_.clear();
    detected_lines_.clear();

    for (const auto& line : lines) {
        float rho = line[0];
        float theta = line[1];

        LineSegment segment;
        segment.rho = rho;
        segment.theta = theta;
        segment.strength = 1.0f; // 可以根据需要计算直线强度

        // 计算直线的起点和终点用于显示
        double a = cos(theta), b = sin(theta);
        double x0 = a * rho, y0 = b * rho;

        // 扩展到图像边界
        int img_diagonal = std::sqrt(image_size_.width * image_size_.width +
            image_size_.height * image_size_.height);
        segment.start = cv::Point2f(x0 + img_diagonal * (-b), y0 + img_diagonal * (a));
        segment.end = cv::Point2f(x0 - img_diagonal * (-b), y0 - img_diagonal * (a));

        detected_lines_.push_back(segment);

        if (isHorizontal(theta)) {
            horizontal_lines_.push_back(segment);
        }
        else if (isVertical(theta)) {
            vertical_lines_.push_back(segment);
        }
    }

    std::cout << "检测到 " << horizontal_lines_.size() << " 条水平线, "
        << vertical_lines_.size() << " 条垂直线" << std::endl;
}

bool HoughLineDetector::selectBoundaryLines() {
    if (horizontal_lines_.size() < 2 || vertical_lines_.size() < 2) {
        return false;
    }

    // 对水平线按y坐标排序（rho * sin(theta)表示到原点的垂直距离）
    std::sort(horizontal_lines_.begin(), horizontal_lines_.end(),
        [](const LineSegment& a, const LineSegment& b) {
        float y_a = a.rho * sin(a.theta);
        float y_b = b.rho * sin(b.theta);
        return y_a < y_b;
    });

    // 对垂直线按x坐标排序（rho * cos(theta)表示到原点的水平距离）
    std::sort(vertical_lines_.begin(), vertical_lines_.end(),
        [](const LineSegment& a, const LineSegment& b) {
        float x_a = a.rho * cos(a.theta);
        float x_b = b.rho * cos(b.theta);
        return x_a < x_b;
    });

    // 选择最上和最下的水平线
    LineSegment top_line = horizontal_lines_[0];
    LineSegment bottom_line = horizontal_lines_[horizontal_lines_.size() - 1];

    // 选择最左和最右的垂直线
    LineSegment left_line = vertical_lines_[0];
    LineSegment right_line = vertical_lines_[vertical_lines_.size() - 1];

    // 计算四个交点
    rectangle_.topLeft = getIntersection(top_line, left_line);
    rectangle_.topRight = getIntersection(top_line, right_line);
    rectangle_.bottomLeft = getIntersection(bottom_line, left_line);
    rectangle_.bottomRight = getIntersection(bottom_line, right_line);

    return true;
}

bool HoughLineDetector::isHorizontal(float theta) {
    // 水平线的角度接近0或π
    const float tolerance = CV_PI / 6; // 30度容差
    return (theta < tolerance) || (theta > CV_PI - tolerance);
}

bool HoughLineDetector::isVertical(float theta) {
    // 垂直线的角度接近π/2
    const float tolerance = CV_PI / 6; // 30度容差
    return (theta > CV_PI / 2 - tolerance) && (theta < CV_PI / 2 + tolerance);
}

cv::Point2f HoughLineDetector::getIntersection(const LineSegment& line1, const LineSegment& line2) {
    float rho1 = line1.rho, theta1 = line1.theta;
    float rho2 = line2.rho, theta2 = line2.theta;

    float cos1 = cos(theta1), sin1 = sin(theta1);
    float cos2 = cos(theta2), sin2 = sin(theta2);

    float det = cos1 * sin2 - sin1 * cos2;
    if (std::abs(det) < 1e-6) {
        return cv::Point2f(-1, -1); // 平行线，无交点
    }

    float x = (sin2 * rho1 - sin1 * rho2) / det;
    float y = (cos1 * rho2 - cos2 * rho1) / det;

    return cv::Point2f(x, y);
}

bool HoughLineDetector::validateRectangle() {
    // 检查所有交点是否有效
    if (rectangle_.topLeft.x < 0 || rectangle_.topLeft.y < 0 ||
        rectangle_.topRight.x < 0 || rectangle_.topRight.y < 0 ||
        rectangle_.bottomLeft.x < 0 || rectangle_.bottomLeft.y < 0 ||
        rectangle_.bottomRight.x < 0 || rectangle_.bottomRight.y < 0) {
        return false;
    }

    // 检查交点是否在图像范围内
    auto isInBounds = [this](const cv::Point2f& pt) {
        return pt.x >= 0 && pt.x < image_size_.width&&
            pt.y >= 0 && pt.y < image_size_.height;
    };

    if (!isInBounds(rectangle_.topLeft) || !isInBounds(rectangle_.topRight) ||
        !isInBounds(rectangle_.bottomLeft) || !isInBounds(rectangle_.bottomRight)) {
        std::cout << "警告: 部分交点超出图像边界" << std::endl;
        // 不直接返回false，允许部分超出边界的情况
    }

    // 检查矩形的几何合理性
    float width1 = cv::norm(rectangle_.topRight - rectangle_.topLeft);
    float width2 = cv::norm(rectangle_.bottomRight - rectangle_.bottomLeft);
    float height1 = cv::norm(rectangle_.bottomLeft - rectangle_.topLeft);
    float height2 = cv::norm(rectangle_.bottomRight - rectangle_.topRight);

    // 检查对边长度是否相近
    float width_diff = std::abs(width1 - width2) / std::max(width1, width2);
    float height_diff = std::abs(height1 - height2) / std::max(height1, height2);

    if (width_diff > 0.3 || height_diff > 0.3) {
        std::cout << "警告: 矩形形状不规则，宽度差异: " << width_diff
            << ", 高度差异: " << height_diff << std::endl;
    }

    // 检查最小尺寸
    if (std::min(width1, width2) < 10 || std::min(height1, height2) < 10) {
        std::cout << "矩形尺寸过小" << std::endl;
        return false;
    }

    return true;
}

cv::Mat HoughLineDetector::drawResults(const cv::Mat& image, bool drawLines, bool drawRectangle) const {
    cv::Mat result;
    if (image.channels() == 1) {
        cv::cvtColor(image, result, cv::COLOR_GRAY2BGR);
    }
    else {
        result = image.clone();
    }

    if (drawLines) {
        // 绘制所有检测到的直线
        for (const auto& line : detected_lines_) {
            cv::line(result, line.start, line.end, cv::Scalar(0, 255, 0), 1);
        }

        // 高亮显示边界线
        if (horizontal_lines_.size() >= 2) {
            cv::line(result, horizontal_lines_[0].start, horizontal_lines_[0].end,
                cv::Scalar(255, 0, 0), 2); // 蓝色 - 上边界
            cv::line(result, horizontal_lines_[horizontal_lines_.size() - 1].start,
                horizontal_lines_[horizontal_lines_.size() - 1].end,
                cv::Scalar(255, 0, 0), 2); // 蓝色 - 下边界
        }

        if (vertical_lines_.size() >= 2) {
            cv::line(result, vertical_lines_[0].start, vertical_lines_[0].end,
                cv::Scalar(0, 0, 255), 2); // 红色 - 左边界
            cv::line(result, vertical_lines_[vertical_lines_.size() - 1].start,
                vertical_lines_[vertical_lines_.size() - 1].end,
                cv::Scalar(0, 0, 255), 2); // 红色 - 右边界
        }
    }

    if (drawRectangle && rectangle_.isValid) {
        // 绘制矩形
        std::vector<cv::Point2f> corners = {
            rectangle_.topLeft, rectangle_.topRight,
            rectangle_.bottomRight, rectangle_.bottomLeft
        };

        for (int i = 0; i < 4; i++) {
            cv::line(result, corners[i], corners[(i + 1) % 4], cv::Scalar(0, 255, 255), 3);
        }

        // 标记顶点
        cv::circle(result, rectangle_.topLeft, 5, cv::Scalar(255, 255, 0), -1);
        cv::circle(result, rectangle_.topRight, 5, cv::Scalar(255, 255, 0), -1);
        cv::circle(result, rectangle_.bottomLeft, 5, cv::Scalar(255, 255, 0), -1);
        cv::circle(result, rectangle_.bottomRight, 5, cv::Scalar(255, 255, 0), -1);

        // 添加文本标签
        cv::putText(result, "TL", rectangle_.topLeft + cv::Point2f(10, -10),
            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 255, 255), 1);
        cv::putText(result, "TR", rectangle_.topRight + cv::Point2f(10, -10),
            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 255, 255), 1);
        cv::putText(result, "BL", rectangle_.bottomLeft + cv::Point2f(10, 10),
            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 255, 255), 1);
        cv::putText(result, "BR", rectangle_.bottomRight + cv::Point2f(10, 10),
            cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 255, 255), 1);
    }

    return result;
}